namespace Applications.DTOs.MerchantBranchInvoiceAccount;

/// <summary>
/// Response DTO for merchant branch invoice account
/// </summary>
public class MerchantBranchInvoiceAccountResponse
{
    /// <summary>
    /// Unique identifier
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Tax number for the merchant branch
    /// </summary>
    public string TaxNumber { get; set; } = null!;

    /// <summary>
    /// Username for the invoice account
    /// </summary>
    public string InvoiceAccountUserName { get; set; } = null!;

    /// <summary>
    /// Password for the invoice account (masked for security)
    /// </summary>
    public string InvoiceAccountPassword { get; set; } = "***";

    /// <summary>
    /// Invoice account provider (hardcoded as "MBF")
    /// </summary>
    public string InvoiceAccountProvider { get; set; } = null!;

    /// <summary>
    /// When this invoice account becomes effective
    /// </summary>
    public DateTime EffectiveDate { get; set; }

    /// <summary>
    /// When this invoice account expires
    /// </summary>
    public DateTime ExpirationDate { get; set; }

    /// <summary>
    /// Foreign key to MerchantBranch
    /// </summary>
    public Guid MerchantBranchId { get; set; }

    /// <summary>
    /// Merchant branch name for display purposes
    /// </summary>
    public string MerchantBranchName { get; set; } = null!;

    /// <summary>
    /// Foreign key reference to Partner entity's Id
    /// </summary>
    public Guid PartnerId { get; set; }

    /// <summary>
    /// Whether this invoice account is currently active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// When this record was created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Who created this record
    /// </summary>
    public Guid? CreatedBy { get; set; }

    /// <summary>
    /// When this record was last updated
    /// </summary>
    public DateTime? UpdatedAt { get; set; }

    /// <summary>
    /// Who last updated this record
    /// </summary>
    public Guid? UpdatedBy { get; set; }
}
