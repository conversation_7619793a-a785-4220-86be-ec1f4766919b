using System.ComponentModel.DataAnnotations;

namespace Applications.DTOs.MerchantBranchInvoiceAccount;

/// <summary>
/// Request DTO for creating a new merchant branch invoice account
/// </summary>
public class CreateMerchantBranchInvoiceAccountRequest
{
    /// <summary>
    /// Tax number for the merchant branch
    /// </summary>
    [Required(ErrorMessage = "TaxNumber is required")]
    [StringLength(50, ErrorMessage = "TaxNumber cannot exceed 50 characters")]
    public string TaxNumber { get; set; } = null!;

    /// <summary>
    /// Username for the invoice account
    /// </summary>
    [Required(ErrorMessage = "InvoiceAccountUserName is required")]
    [StringLength(100, ErrorMessage = "InvoiceAccountUserName cannot exceed 100 characters")]
    public string InvoiceAccountUserName { get; set; } = null!;

    /// <summary>
    /// Password for the invoice account
    /// </summary>
    [Required(ErrorMessage = "InvoiceAccountPassword is required")]
    [StringLength(500, MinimumLength = 6, ErrorMessage = "InvoiceAccountPassword must be between 6 and 500 characters")]
    public string InvoiceAccountPassword { get; set; } = null!;

    /// <summary>
    /// Invoice account provider (hardcoded as "MBF")
    /// </summary>
    // public string InvoiceAccountProvider { get; set; } = "MBF";

    /// <summary>
    /// When this invoice account becomes effective
    /// </summary>
    [Required(ErrorMessage = "EffectiveDate is required")]
    public DateTime EffectiveDate { get; set; }

    /// <summary>
    /// When this invoice account expires
    /// </summary>
    [Required(ErrorMessage = "ExpirationDate is required")]
    public DateTime ExpirationDate { get; set; }

    /// <summary>
    /// Foreign key to MerchantBranch
    /// </summary>
    // [Required(ErrorMessage = "MerchantBranchId is required")]
    // public Guid MerchantBranchId { get; set; }

    /// <summary>
    /// Merchant branch name for display purposes
    /// </summary>
    [Required(ErrorMessage = "MerchantBranchName is required")]
    [StringLength(200, ErrorMessage = "MerchantBranchName cannot exceed 200 characters")]
    public string MerchantBranchName { get; set; } = null!;

    /// <summary>
    /// Foreign key reference to Partner entity's Id
    /// </summary>
    // [Required(ErrorMessage = "PartnerId is required")]
    // public Guid PartnerId { get; set; }

    /// <summary>
    /// Whether this invoice account is currently active
    /// </summary>
    public bool IsActive { get; set; } = true;
}
