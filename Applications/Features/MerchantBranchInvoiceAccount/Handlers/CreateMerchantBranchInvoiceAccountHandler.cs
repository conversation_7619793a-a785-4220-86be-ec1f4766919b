﻿using Applications.DTOs.MerchantBranchInvoiceAccount;
using Applications.Features.MerchantBranchInvoiceAccount.Commands;
using Applications.Interfaces;
using Mapster;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Shared.Constants;
using BuildingBlocks.Abstractions;

namespace Applications.Features.MerchantBranchInvoiceAccount.Handlers;

/// <summary>
/// Handler for CreateMerchantBranchInvoiceAccountCommand
/// </summary>
public class CreateMerchantBranchInvoiceAccountHandler(
    IApplicationDbContext dbContext,
    ILogger<CreateMerchantBranchInvoiceAccountHandler> logger)
    : IRequestHandler<CreateMerchantBranchInvoiceAccountCommand, Response<MerchantBranchInvoiceAccountResponse>>
{
    public async Task<Response<MerchantBranchInvoiceAccountResponse>> Handle(
        CreateMerchantBranchInvoiceAccountCommand request,
        CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Creating new merchant branch invoice account for TaxNumber: {TaxNumber}", 
                request.Request.TaxNumber);

            // Check if account with same TaxNumber already exists
            var existingAccount = await dbContext.MerchantBranchInvoiceAccounts
                .FirstOrDefaultAsync(x => x.TaxNumber == request.Request.TaxNumber, cancellationToken);

            if (existingAccount != null)
            {
                logger.LogWarning("Merchant branch invoice account with TaxNumber {TaxNumber} already exists",
                    request.Request.TaxNumber);
                return new Response<MerchantBranchInvoiceAccountResponse>
                {
                    Code = ErrorCodes.BAD_REQUEST_ERROR,
                    Message = "An account with this tax number already exists"
                };
            }

            // Check if the merchant branch exists
            // var merchantBranchInvoiceAccount = await dbContext.MerchantBranchInvoiceAccounts
            //     .FirstOrDefaultAsync(x => x.MerchantBranchId == request.Request.MerchantBranchId, cancellationToken);

            // if (merchantBranchInvoiceAccount != null)
            // {
            //     logger.LogWarning("Merchant branch invoice account with TaxNumber {TaxNumber} already exists",
            //         request.Request.TaxNumber);
            //     return new Response<MerchantBranchInvoiceAccountResponse>
            //     {
            //         Code = ErrorCodes.BAD_REQUEST_ERROR,
            //         Message = "An account with this tax number already exists"
            //     };
            // }

            // Create new entity
            var entity = new Core.Entities.MerchantBranchInvoiceAccount
            {
                Id = Guid.NewGuid(),
                TaxNumber = request.Request.TaxNumber,
                InvoiceAccountUserName = request.Request.InvoiceAccountUserName,
                InvoiceAccountPassword = request.Request.InvoiceAccountPassword, 
                InvoiceAccountProvider = "MBF",
                EffectiveDate = request.Request.EffectiveDate,
                ExpirationDate = request.Request.ExpirationDate,
                MerchantBranchId = Guid.NewGuid(),
                MerchantBranchName = request.Request.MerchantBranchName,
                PartnerId = new Guid("*************-5555-5555-************"),
                IsActive = request.Request.IsActive,
                CreatedAt = DateTime.UtcNow
            };

            // Save to database
            await dbContext.MerchantBranchInvoiceAccounts.AddAsync(entity, cancellationToken);
            await dbContext.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Successfully created merchant branch invoice account with Id: {Id}", entity.Id);

            // Map to response DTO using Mapster
            var response = entity.Adapt<MerchantBranchInvoiceAccountResponse>();

            return new Response<MerchantBranchInvoiceAccountResponse>(
                response,
                "Merchant branch invoice account created successfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error creating merchant branch invoice account for TaxNumber: {TaxNumber}",
                request.Request.TaxNumber);
            var errorResponse = new Response<MerchantBranchInvoiceAccountResponse>();
            errorResponse.Code = ErrorCodes.INTERNAL_SERVER_ERROR;
            errorResponse.Message = "An error occurred while creating the merchant branch invoice account";
            return errorResponse;
        }
    }
}
