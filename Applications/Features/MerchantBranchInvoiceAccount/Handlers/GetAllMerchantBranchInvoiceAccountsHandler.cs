﻿using Applications.DTOs.MerchantBranchInvoiceAccount;
using Applications.Features.MerchantBranchInvoiceAccount.Queries;
using Applications.Interfaces;
using Mapster;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Shared.Constants;
using BuildingBlocks.Abstractions;

namespace Applications.Features.MerchantBranchInvoiceAccount.Handlers;

/// <summary>
/// Handler for GetAllMerchantBranchInvoiceAccountsQuery
/// </summary>
public class GetAllMerchantBranchInvoiceAccountsHandler(
    IApplicationDbContext dbContext,
    ILogger<GetAllMerchantBranchInvoiceAccountsHandler> logger)
    : IRequestHandler<GetAllMerchantBranchInvoiceAccountsQuery, Response<List<MerchantBranchInvoiceAccountResponse>>>
{
    public async Task<Response<List<MerchantBranchInvoiceAccountResponse>>> Handle(
        GetAllMerchantBranchInvoiceAccountsQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Retrieving all merchant branch invoice accounts");

            var entities = await dbContext.MerchantBranchInvoiceAccounts
                .Where(x => !x.IsDeleted) // Apply soft delete filter
                .OrderByDescending(x => x.CreatedAt)
                .ToListAsync(cancellationToken);

            logger.LogInformation("Found {Count} merchant branch invoice accounts", entities.Count);

            var responses = entities.Adapt<List<MerchantBranchInvoiceAccountResponse>>();

            return new Response<List<MerchantBranchInvoiceAccountResponse>>(
                responses,
                $"Successfully retrieved {responses.Count} merchant branch invoice accounts");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving merchant branch invoice accounts");
            var errorResponse = new Response<List<MerchantBranchInvoiceAccountResponse>>();
            errorResponse.Code = ErrorCodes.INTERNAL_SERVER_ERROR;
            errorResponse.Message = "An error occurred while retrieving merchant branch invoice accounts";
            return errorResponse;
        }
    }
}
