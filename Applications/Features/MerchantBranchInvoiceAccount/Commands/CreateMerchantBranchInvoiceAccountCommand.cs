﻿using Applications.DTOs.MerchantBranchInvoiceAccount;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;

namespace Applications.Features.MerchantBranchInvoiceAccount.Commands;

/// <summary>
/// Command to create a new merchant branch invoice account
/// </summary>
public record CreateMerchantBranchInvoiceAccountCommand(
    CreateMerchantBranchInvoiceAccountRequest Request) : IRequest<Response<MerchantBranchInvoiceAccountResponse>>;

/// <summary>
/// Validator for CreateMerchantBranchInvoiceAccountCommand
/// </summary>
// Temporarily disabled to avoid ValidationBehavior issues
public class CreateMerchantBranchInvoiceAccountCommandValidator : AbstractValidator<CreateMerchantBranchInvoiceAccountCommand>
{
    public CreateMerchantBranchInvoiceAccountCommandValidator()
    {
        RuleFor(x => x.Request)
            .NotNull()
            .WithMessage("Request cannot be null");

        When(x => x.Request != null, () =>
        {
            RuleFor(x => x.Request.TaxNumber)
                .NotEmpty()
                .WithMessage("TaxNumber is required")
                .MaximumLength(50)
                .WithMessage("TaxNumber cannot exceed 50 characters");

            RuleFor(x => x.Request.InvoiceAccountUserName)
                .NotEmpty()
                .WithMessage("InvoiceAccountUserName is required")
                .MaximumLength(100)
                .WithMessage("InvoiceAccountUserName cannot exceed 100 characters");

            RuleFor(x => x.Request.InvoiceAccountPassword)
                .NotEmpty()
                .WithMessage("InvoiceAccountPassword is required")
                .MinimumLength(6)
                .WithMessage("InvoiceAccountPassword must be at least 6 characters")
                .MaximumLength(500)
                .WithMessage("InvoiceAccountPassword cannot exceed 500 characters");

            RuleFor(x => x.Request.EffectiveDate)
                .NotEmpty()
                .WithMessage("EffectiveDate is required");

            RuleFor(x => x.Request.ExpirationDate)
                .NotEmpty()
                .WithMessage("ExpirationDate is required")
                .GreaterThan(x => x.Request.EffectiveDate)
                .WithMessage("ExpirationDate must be after EffectiveDate");

            // RuleFor(x => x.Request.MerchantBranchId)
            //     .NotEmpty()
            //     .WithMessage("MerchantBranchId is required");

            RuleFor(x => x.Request.MerchantBranchName)
                .NotEmpty()
                .WithMessage("MerchantBranchName is required")
                .MaximumLength(200)
                .WithMessage("MerchantBranchName cannot exceed 200 characters");

            // RuleFor(x => x.Request.PartnerId)
            //     .NotEmpty()
            //     .WithMessage("PartnerId is required");

            // RuleFor(x => x.Request.InvoiceAccountProvider)
            //     .Equal("MBF")
            //     .WithMessage("InvoiceAccountProvider must be 'MBF'");
        });
    }
}
