using Applications.DTOs.MerchantInvoiceOrder.GetInvoiceStatistics;
using Applications.Interfaces.Repositories;
using BuildingBlocks.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Applications.Features.MerchantInvoiceOrder.Queries;

/// <summary>
/// Query để lấy thống kê số lượng hóa đơn của MerchantBranch
/// </summary>
public record GetInvoiceStatisticsQuery(GetInvoiceStatisticsRequest Request) 
    : IRequest<Response<GetInvoiceStatisticsResponse>>;

public class GetInvoiceStatisticsQueryHandler(
    IMerchantInvoiceOrderRepository merchantInvoiceOrderRepository,
    ILogger<GetInvoiceStatisticsQueryHandler> logger)
    : IRequestHandler<GetInvoiceStatisticsQuery, Response<GetInvoiceStatisticsResponse>>
{
    public async Task<Response<GetInvoiceStatisticsResponse>> Handle(
        GetInvoiceStatisticsQuery request, 
        CancellationToken cancellationToken)
    {
        try
        {
            var merchantBranchId = request.Request.MerchantBranchId;
            var currentDate = DateTime.UtcNow;

            // Lấy order đang hoạt động duy nhất (active và còn trong thời gian hiệu lực)
            var activeOrder = await merchantInvoiceOrderRepository
                .AsQueryable()
                .Where(o => o.MerchantBranchId == merchantBranchId &&
                           o.IsActive &&
                           o.EffectiveDateTo >= currentDate)
                .OrderByDescending(o => o.CreatedAt) // Lấy order mới nhất nếu có nhiều hơn 1
                .FirstOrDefaultAsync(cancellationToken);

            if (activeOrder == null)
            {
                // Kiểm tra xem có order nào đã hết hạn không
                var hasExpiredOrder = await merchantInvoiceOrderRepository
                    .AsQueryable()
                    .AnyAsync(o => o.MerchantBranchId == merchantBranchId &&
                                  o.IsActive &&
                                  o.EffectiveDateTo < currentDate, cancellationToken);

                var message = hasExpiredOrder ?
                    "Order hiện tại đã hết hạn. Vui lòng mua order mới!" :
                    "Merchant Branch chưa có order nào đang hoạt động!";

                return new Response<GetInvoiceStatisticsResponse>(new GetInvoiceStatisticsResponse
                {
                    MerchantBranchId = merchantBranchId,
                    TotalPurchasedInvoices = 0,
                    UsedInvoices = 0,
                    AvailableInvoices = 0,
                    ExpiredInvoices = 0,
                    Message = message
                });
            }

            // Tính toán thống kê từ order đang hoạt động duy nhất
            var totalPurchased = activeOrder.TotalInvoiceQuantity;
            var availableInvoices = activeOrder.RemainingInvoiceQuantity;
            var usedInvoices = totalPurchased - availableInvoices;

            // Kiểm tra trạng thái order
            string? statusMessage = null;
            if (availableInvoices <= 0)
            {
                statusMessage = "Order đã sử dụng hết hóa đơn!";
            }
            else if (activeOrder.EffectiveDateTo.Date == currentDate.Date)
            {
                statusMessage = "Order sẽ hết hạn hôm nay!";
            }
            else if (activeOrder.EffectiveDateTo <= currentDate.AddDays(7))
            {
                var daysLeft = (activeOrder.EffectiveDateTo.Date - currentDate.Date).Days;
                statusMessage = $"Order sẽ hết hạn trong {daysLeft} ngày!";
            }

            var response = new GetInvoiceStatisticsResponse
            {
                MerchantBranchId = merchantBranchId,
                TotalPurchasedInvoices = totalPurchased,
                UsedInvoices = usedInvoices,
                AvailableInvoices = availableInvoices,
                ExpiredInvoices = 0, // Không có expired vì chỉ lấy order đang hoạt động
                Message = statusMessage
            };

            logger.LogInformation("Retrieved invoice statistics for MerchantBranchId {MerchantBranchId}: " +
                                 "Total: {Total}, Used: {Used}, Available: {Available}, OrderId: {OrderId}",
                merchantBranchId, totalPurchased, usedInvoices, availableInvoices, activeOrder.Id);

            return new Response<GetInvoiceStatisticsResponse>(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while getting invoice statistics for MerchantBranchId {MerchantBranchId}",
                request.Request.MerchantBranchId);
            return new Response<GetInvoiceStatisticsResponse>
            {
                Code = "500",
                Message = "An error occurred while retrieving invoice statistics"
            };
        }
    }
}
