using Applications.DTOs.MerchantInvoiceOrder.GetMerchantInvoiceOrderDetail;
using Applications.Interfaces.Repositories;
using BuildingBlocks.Abstractions;
using Mapster;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Applications.Features.MerchantInvoiceOrder.Queries;

/// <summary>
/// Query để lấy chi tiết MerchantInvoiceOrder
/// </summary>
public record GetMerchantInvoiceOrderDetailQuery(Guid Id) 
    : IRequest<Response<GetMerchantInvoiceOrderDetailResponse>>;

public class GetMerchantInvoiceOrderDetailQueryHandler(
    IMerchantInvoiceOrderRepository merchantInvoiceOrderRepository,
    IInvoiceInfoRepository invoiceInfoRepository,
    ILogger<GetMerchantInvoiceOrderDetailQueryHandler> logger)
    : IRequestHandler<GetMerchantInvoiceOrderDetailQuery, Response<GetMerchantInvoiceOrderDetailResponse>>
{
    public async Task<Response<GetMerchantInvoiceOrderDetailResponse>> Handle(
        GetMerchantInvoiceOrderDetailQuery request, 
        CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Getting MerchantInvoiceOrder detail for Id: {Id}", request.Id);

            // Lấy MerchantInvoiceOrder với includes
            var order = await merchantInvoiceOrderRepository
                .AsQueryable()
                .Include(o => o.MerchantBranchInvoiceAccount)
                .FirstOrDefaultAsync(o => o.Id == request.Id, cancellationToken);

            if (order == null)
            {
                logger.LogWarning("MerchantInvoiceOrder not found with Id: {Id}", request.Id);
                return new Response<GetMerchantInvoiceOrderDetailResponse>
                {
                    Code = "404",
                    Message = "MerchantInvoiceOrder not found"
                };
            }

            // Đếm số lượng hóa đơn đã sử dụng
            var usedInvoiceCount = await invoiceInfoRepository
                .AsQueryable()
                .CountAsync(i => i.MerchantInvoiceOrderId == request.Id, cancellationToken);

            // Kiểm tra trạng thái hết hạn
            var isExpired = order.EffectiveDateTo < DateTime.UtcNow;

            // Map to response
            var response = new GetMerchantInvoiceOrderDetailResponse
            {
                Id = order.Id,
                MerchantBranchId = order.MerchantBranchId,
                TotalInvoiceQuantity = order.TotalInvoiceQuantity,
                RemainingInvoiceQuantity = order.RemainingInvoiceQuantity,
                EffectiveDateFrom = order.EffectiveDateFrom,
                EffectiveDateTo = order.EffectiveDateTo,
                Description = order.Description,
                IsActive = order.IsActive,
                OrderReference = order.OrderReference,
                CreatedAt = order.CreatedAt,
                UpdatedAt = order.UpdatedAt ?? order.CreatedAt,
                UsedInvoiceCount = usedInvoiceCount,
                IsExpired = isExpired,
                MerchantBranch = order.MerchantBranchInvoiceAccount != null ? new MerchantBranchDetail
                {
                    Id = order.MerchantBranchInvoiceAccount.Id,
                    BranchName = order.MerchantBranchInvoiceAccount.MerchantBranchName,
                    BranchCode = order.MerchantBranchInvoiceAccount.TaxNumber,
                    Address = null, // Not available in current entity
                    ContactEmail = null, // Not available in current entity
                    ContactPhone = null // Not available in current entity
                } : null
            };

            logger.LogInformation("Successfully retrieved MerchantInvoiceOrder detail for Id: {Id}", request.Id);
            return new Response<GetMerchantInvoiceOrderDetailResponse>(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while getting MerchantInvoiceOrder detail for Id: {Id}", request.Id);
            return new Response<GetMerchantInvoiceOrderDetailResponse>
            {
                Code = "500",
                Message = "An error occurred while retrieving MerchantInvoiceOrder detail"
            };
        }
    }
}
