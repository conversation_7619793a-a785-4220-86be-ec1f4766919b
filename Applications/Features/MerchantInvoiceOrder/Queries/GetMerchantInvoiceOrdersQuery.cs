using Applications.DTOs.MerchantInvoiceOrder.GetMerchantInvoiceOrders;
using Applications.Interfaces.Repositories;
using BuildingBlocks.Abstractions;
using Mapster;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Applications.Features.MerchantInvoiceOrder.Queries;

/// <summary>
/// Query để lấy danh sách MerchantInvoiceOrder với phân trang và filter
/// </summary>
public record GetMerchantInvoiceOrdersQuery(GetMerchantInvoiceOrdersRequest Request) 
    : IRequest<PaginationResponse<MerchantInvoiceOrderDto>>;

public class GetMerchantInvoiceOrdersQueryHandler(
    IMerchantInvoiceOrderRepository merchantInvoiceOrderRepository,
    ILogger<GetMerchantInvoiceOrdersQueryHandler> logger)
    : IRequestHandler<GetMerchantInvoiceOrdersQuery, PaginationResponse<MerchantInvoiceOrderDto>>
{
    public async Task<PaginationResponse<MerchantInvoiceOrderDto>> Handle(
        GetMerchantInvoiceOrdersQuery request, 
        CancellationToken cancellationToken)
    {
        try
        {
            var req = request.Request;
            
            logger.LogInformation("Getting MerchantInvoiceOrders with filters - " +
                                 "MerchantBranchId: {MerchantBranchId}, IsActive: {IsActive}, " +
                                 "PageIndex: {PageIndex}, PageSize: {PageSize}",
                req.MerchantBranchId, req.IsActive, req.PageIndex, req.PageSize);

            // Build query với includes
            var query = merchantInvoiceOrderRepository
                .AsQueryable()
                .Include(o => o.MerchantBranchInvoiceAccount)
                .AsQueryable();

            // Apply filters
            if (req.MerchantBranchId.HasValue)
            {
                query = query.Where(o => o.MerchantBranchId == req.MerchantBranchId.Value);
            }

            if (req.IsActive.HasValue)
            {
                query = query.Where(o => o.IsActive == req.IsActive.Value);
                // Note: Theo nghiệp vụ, mỗi MerchantBranch chỉ có tối đa 1 order active tại một thời điểm
            }

            if (req.FromEffectiveDate.HasValue)
            {
                query = query.Where(o => o.EffectiveDateFrom >= req.FromEffectiveDate.Value);
            }

            if (req.ToEffectiveDate.HasValue)
            {
                query = query.Where(o => o.EffectiveDateTo <= req.ToEffectiveDate.Value);
            }

            if (!string.IsNullOrWhiteSpace(req.SearchTerm))
            {
                var searchTerm = req.SearchTerm.Trim().ToLower();
                query = query.Where(o => 
                    (o.OrderReference != null && o.OrderReference.ToLower().Contains(searchTerm)) ||
                    (o.Description != null && o.Description.ToLower().Contains(searchTerm)));
            }

            // Order by CreatedAt descending (mới nhất đến cũ nhất)
            query = query.OrderByDescending(o => o.CreatedAt);

            // Get total count
            var totalCount = await query.CountAsync(cancellationToken);

            // Apply pagination và mapping
            var items = await query
                .Skip((req.PageIndex - 1) * req.PageSize)
                .Take(req.PageSize)
                .Select(o => new MerchantInvoiceOrderDto
                {
                    Id = o.Id,
                    MerchantBranchId = o.MerchantBranchId,
                    TotalInvoiceQuantity = o.TotalInvoiceQuantity,
                    RemainingInvoiceQuantity = o.RemainingInvoiceQuantity,
                    EffectiveDateFrom = o.EffectiveDateFrom,
                    EffectiveDateTo = o.EffectiveDateTo,
                    Description = o.Description,
                    IsActive = o.IsActive,
                    OrderReference = o.OrderReference,
                    CreatedAt = o.CreatedAt,
                    UpdatedAt = o.UpdatedAt ?? o.CreatedAt,
                    MerchantBranch = o.MerchantBranchInvoiceAccount != null ? new MerchantBranchSummary
                    {
                        Id = o.MerchantBranchInvoiceAccount.Id,
                        BranchName = o.MerchantBranchInvoiceAccount.MerchantBranchName,
                        BranchCode = o.MerchantBranchInvoiceAccount.TaxNumber
                    } : null
                })
                .ToListAsync(cancellationToken);

            var result = new PaginationResponse<MerchantInvoiceOrderDto>(
                req.PageIndex,
                req.PageSize,
                totalCount,
                items);

            logger.LogInformation("Successfully retrieved {Count} MerchantInvoiceOrders out of {Total} total for page {PageIndex}",
                result.Data?.Count() ?? 0, result.Total, req.PageIndex);

            return result;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while retrieving MerchantInvoiceOrders");
            return new PaginationResponse<MerchantInvoiceOrderDto>(
                request.Request.PageIndex,
                request.Request.PageSize,
                0,
                [])
            {
                Code = "500",
                Message = "An error occurred while retrieving MerchantInvoiceOrders"
            };
        }
    }
}
