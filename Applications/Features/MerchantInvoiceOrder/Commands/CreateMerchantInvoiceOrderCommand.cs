using Applications.DTOs.MerchantInvoiceOrder.CreateMerchantInvoiceOrder;
using Applications.Interfaces;
using Applications.Interfaces.Repositories;
using BuildingBlocks.Abstractions;
using Mapster;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Applications.Features.MerchantInvoiceOrder.Commands;

/// <summary>
/// Command để tạo MerchantInvoiceOrder mới
/// </summary>
public record CreateMerchantInvoiceOrderCommand(CreateMerchantInvoiceOrderRequest Request) 
    : IRequest<Response<CreateMerchantInvoiceOrderResponse>>;

public class CreateMerchantInvoiceOrderCommandHandler(
    IApplicationDbContext dbContext,
    ILogger<CreateMerchantInvoiceOrderCommandHandler> logger)
    : IRequestHandler<CreateMerchantInvoiceOrderCommand, Response<CreateMerchantInvoiceOrderResponse>>
{
    public async Task<Response<CreateMerchantInvoiceOrderResponse>> Handle(
        CreateMerchantInvoiceOrderCommand request, 
        CancellationToken cancellationToken)
    {
        try
        {
            var req = request.Request;
            
            logger.LogInformation("Creating MerchantInvoiceOrder for MerchantBranchId: {MerchantBranchId}", 
                req.MerchantBranchId);

            // Validate date range
            if (req.EffectiveDateFrom >= req.EffectiveDateTo)
            {
                return new Response<CreateMerchantInvoiceOrderResponse>
                {
                    Code = "400",
                    Message = "EffectiveDateFrom must be earlier than EffectiveDateTo"
                };
            }

            // Validate MerchantBranchId exists in MerchantBranchInvoiceAccount
            var merchantBranchExists = await dbContext.MerchantBranchInvoiceAccounts
                .FirstOrDefaultAsync(mb => mb.MerchantBranchId == req.MerchantBranchId, cancellationToken);

            if (merchantBranchExists == null)
            {
                logger.LogWarning("MerchantBranchId {MerchantBranchId} not found in MerchantBranchInvoiceAccount",
                    req.MerchantBranchId);
                return new Response<CreateMerchantInvoiceOrderResponse>
                {
                    Code = "404",
                    Message = "MerchantBranchId must exist in MerchantBranchInvoiceAccount"
                };
            }

            // 🔥 CRITICAL: Validate không có order nào đang active cho MerchantBranch này
            var currentDate = DateTime.UtcNow;
            var existingActiveOrder = await dbContext.MerchantInvoiceOrders
                .Where(o => o.MerchantBranchId == req.MerchantBranchId &&
                           o.IsActive &&
                           o.EffectiveDateTo >= currentDate)
                .FirstOrDefaultAsync(cancellationToken);

            if (existingActiveOrder != null)
            {
                logger.LogWarning("MerchantBranchId {MerchantBranchId} already has an active order {OrderId} valid until {EffectiveDateTo}",
                    req.MerchantBranchId, existingActiveOrder.Id, existingActiveOrder.EffectiveDateTo);
                return new Response<CreateMerchantInvoiceOrderResponse>
                {
                    Code = "409", // Conflict
                    Message = $"MerchantBranch đã có order đang hoạt động (ID: {existingActiveOrder.Id}) có hiệu lực đến {existingActiveOrder.EffectiveDateTo:dd/MM/yyyy}. Không thể tạo order mới!"
                };
            }

            // Create new MerchantInvoiceOrder
            var newOrder = new Core.Entities.MerchantInvoiceOrder
            {
                Id = Guid.NewGuid(),
                MerchantBranchId = req.MerchantBranchId,
                TotalInvoiceQuantity = req.TotalInvoiceQuantity,
                RemainingInvoiceQuantity = req.TotalInvoiceQuantity, // Initially same as total
                EffectiveDateFrom = req.EffectiveDateFrom,
                EffectiveDateTo = req.EffectiveDateTo,
                Description = req.Description,
                OrderReference = req.OrderReference,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = Guid.NewGuid() // TODO: Get from current user context
            };

            // Save to database
            await dbContext.MerchantInvoiceOrders.AddAsync(newOrder, cancellationToken);
            await dbContext.SaveChangesAsync(cancellationToken);

            // Map to response
            // var response = new CreateMerchantInvoiceOrderResponse
            // {
            //     Id = newOrder.Id,
            //     MerchantBranchId = newOrder.MerchantBranchId,
            //     TotalInvoiceQuantity = newOrder.TotalInvoiceQuantity,
            //     RemainingInvoiceQuantity = newOrder.RemainingInvoiceQuantity,
            //     EffectiveDateFrom = newOrder.EffectiveDateFrom,
            //     EffectiveDateTo = newOrder.EffectiveDateTo,
            //     Description = newOrder.Description,
            //     OrderReference = newOrder.OrderReference,
            //     CreatedAt = newOrder.CreatedAt
            // };

            var response = newOrder.Adapt<CreateMerchantInvoiceOrderResponse>();

            logger.LogInformation("Successfully created MerchantInvoiceOrder with Id: {Id} for MerchantBranchId: {MerchantBranchId}",
                newOrder.Id, req.MerchantBranchId);

            return new Response<CreateMerchantInvoiceOrderResponse>(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while creating MerchantInvoiceOrder for MerchantBranchId: {MerchantBranchId}",
                request.Request.MerchantBranchId);
            return new Response<CreateMerchantInvoiceOrderResponse>
            {
                Code = "500",
                Message = "An error occurred while creating MerchantInvoiceOrder"
            };
        }
    }
}
