using Applications.DTOs.MerchantInvoiceOrder.CreateMerchantInvoiceOrder;
using Applications.DTOs.MerchantInvoiceOrder.GetInvoiceStatistics;
using Applications.DTOs.MerchantInvoiceOrder.GetMerchantInvoiceOrders;
using Applications.Features.MerchantInvoiceOrder.Commands;
using Applications.Features.MerchantInvoiceOrder.Queries;
using BuildingBlocks.Abstractions;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Shared.Constants;

namespace API.Controllers;

/// <summary>
/// Controller cho quản lý MerchantInvoiceOrder
/// Uses Simple Bearer Token authentication - bypasses B2B authentication complexity
/// </summary>
[Authorize(Policy = "SimpleBearerAuth")]
public class MerchantInvoiceOrderController(IMediator mediator, ILogger<MerchantInvoiceOrderController> logger) : BaseApiController
{
    /// <summary>
    /// API lấy số lượng hóa đơn đã sử dụng và số lượng hóa đơn có sẵn của một MerchantBranchId
    /// </summary>
    /// <param name="merchantBranchId">ID của MerchantBranch</param>
    /// <returns>Thống kê số lượng hóa đơn</returns>
    [HttpGet("statistics/{merchantBranchId:guid}")]
    public async Task<IActionResult> GetInvoiceStatisticsAsync([FromRoute] Guid merchantBranchId)
    {
        try
        {
            logger.LogInformation("Getting invoice statistics for MerchantBranchId: {MerchantBranchId}", merchantBranchId);

            var request = new GetInvoiceStatisticsRequest { MerchantBranchId = merchantBranchId };
            var query = new GetInvoiceStatisticsQuery(request);
            var result = await mediator.Send(query);

            logger.LogInformation("Successfully retrieved invoice statistics for MerchantBranchId: {MerchantBranchId}", merchantBranchId);
            return Ok(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while retrieving invoice statistics for MerchantBranchId: {MerchantBranchId}", merchantBranchId);
            return StatusCode(500, new Response<object>
            {
                Message = "An internal server error occurred while retrieving invoice statistics",
                Code = ErrorCodes.INTERNAL_SERVER_ERROR
            });
        }
    }

    /// <summary>
    /// API danh sách MerchantInvoiceOrder có phân trang và filter theo MerchantBranchId
    /// </summary>
    /// <param name="request">Thông tin lọc và phân trang</param>
    /// <returns>Danh sách MerchantInvoiceOrder có phân trang</returns>
    [HttpGet]
    public async Task<IActionResult> GetMerchantInvoiceOrdersAsync([FromQuery] GetMerchantInvoiceOrdersRequest request)
    {
        try
        {
            logger.LogInformation("Getting MerchantInvoiceOrders with filters - " +
                                 "MerchantBranchId: {MerchantBranchId}, IsActive: {IsActive}, " +
                                 "PageIndex: {PageIndex}, PageSize: {PageSize}",
                request.MerchantBranchId, request.IsActive, request.PageIndex, request.PageSize);

            // Validate date range
            if (request.FromEffectiveDate.HasValue && request.ToEffectiveDate.HasValue && 
                request.FromEffectiveDate.Value > request.ToEffectiveDate.Value)
            {
                logger.LogWarning("Invalid date range: FromEffectiveDate {FromDate} is greater than ToEffectiveDate {ToDate}",
                    request.FromEffectiveDate.Value, request.ToEffectiveDate.Value);
                return BadRequest(new Response<object>
                {
                    Message = "FromEffectiveDate cannot be greater than ToEffectiveDate",
                    Code = ErrorCodes.BAD_REQUEST_ERROR
                });
            }

            var query = new GetMerchantInvoiceOrdersQuery(request);
            var result = await mediator.Send(query);

            logger.LogInformation("Successfully retrieved {Count} MerchantInvoiceOrders out of {Total} total for page {PageIndex}",
                result.Data?.Count() ?? 0, result.Total, request.PageIndex);
            return Ok(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while retrieving MerchantInvoiceOrders");
            return StatusCode(500, new Response<object>
            {
                Message = "An internal server error occurred while retrieving MerchantInvoiceOrders",
                Code = ErrorCodes.INTERNAL_SERVER_ERROR
            });
        }
    }

    /// <summary>
    /// API tạo một MerchantInvoiceOrder mới
    /// </summary>
    /// <param name="request">Thông tin MerchantInvoiceOrder cần tạo</param>
    /// <returns>Thông tin MerchantInvoiceOrder vừa tạo</returns>
    [HttpPost]
    public async Task<IActionResult> CreateMerchantInvoiceOrderAsync([FromBody] CreateMerchantInvoiceOrderRequest request)
    {
        try
        {
            logger.LogInformation("Creating MerchantInvoiceOrder for MerchantBranchId: {MerchantBranchId}", request.MerchantBranchId);

            var command = new CreateMerchantInvoiceOrderCommand(request);
            var result = await mediator.Send(command);

            if (result.Code == "000" && result.Data != null)
            {
                logger.LogInformation("Successfully created MerchantInvoiceOrder with Id: {Id}", result.Data.Id);
                return Created($"/zenInvoice/api/MerchantInvoiceOrder/{result.Data.Id}", result);
            }

            return BadRequest(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while creating MerchantInvoiceOrder");
            return StatusCode(500, new Response<object>
            {
                Message = "An internal server error occurred while creating MerchantInvoiceOrder",
                Code = ErrorCodes.INTERNAL_SERVER_ERROR
            });
        }
    }

    /// <summary>
    /// API lấy chi tiết một MerchantInvoiceOrder dựa trên Id
    /// </summary>
    /// <param name="id">ID của MerchantInvoiceOrder</param>
    /// <returns>Chi tiết MerchantInvoiceOrder</returns>
    [HttpGet("{id:guid}")]
    public async Task<IActionResult> GetMerchantInvoiceOrderDetailAsync([FromRoute] Guid id)
    {
        try
        {
            logger.LogInformation("Getting MerchantInvoiceOrder detail for Id: {Id}", id);

            var query = new GetMerchantInvoiceOrderDetailQuery(id);
            var result = await mediator.Send(query);

            if (result.Code == "000" && result.Data != null)
            {
                logger.LogInformation("Successfully retrieved MerchantInvoiceOrder detail for Id: {Id}", id);
                return Ok(result);
            }

            return NotFound(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while retrieving MerchantInvoiceOrder detail for Id: {Id}", id);
            return StatusCode(500, new Response<object>
            {
                Message = "An internal server error occurred while retrieving MerchantInvoiceOrder detail",
                Code = ErrorCodes.INTERNAL_SERVER_ERROR
            });
        }
    }

    /// <summary>
    /// API xóa một MerchantInvoiceOrder by Id
    /// </summary>
    /// <param name="id">ID của MerchantInvoiceOrder cần xóa</param>
    /// <returns>Kết quả xóa</returns>
    [HttpDelete("{id:guid}")]
    public async Task<IActionResult> DeleteMerchantInvoiceOrderAsync([FromRoute] Guid id)
    {
        try
        {
            logger.LogInformation("Deleting MerchantInvoiceOrder with Id: {Id}", id);

            var command = new DeleteMerchantInvoiceOrderCommand(id);
            var result = await mediator.Send(command);

            if (result.Code == "000" && result.Data == true)
            {
                logger.LogInformation("Successfully deleted MerchantInvoiceOrder with Id: {Id}", id);
                return Ok(result);
            }

            return BadRequest(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while deleting MerchantInvoiceOrder with Id: {Id}", id);
            return StatusCode(500, new Response<object>
            {
                Message = "An internal server error occurred while deleting MerchantInvoiceOrder",
                Code = ErrorCodes.INTERNAL_SERVER_ERROR
            });
        }
    }

    // TODO: Uncomment when DeactivateMerchantInvoiceOrderCommand is properly recognized
    // /// <summary>
    // /// Deactivate một MerchantInvoiceOrder (set IsActive = false)
    // /// </summary>
    // /// <param name="id">ID của MerchantInvoiceOrder cần deactivate</param>
    // /// <returns>Kết quả deactivate</returns>
    // [HttpPatch("{id:guid}/deactivate")]
    // [Microsoft.AspNetCore.Authorization.AllowAnonymous]
    // public async Task<IActionResult> DeactivateMerchantInvoiceOrderAsync(Guid id)
    // {
    //     try
    //     {
    //         logger.LogInformation("Deactivating MerchantInvoiceOrder with Id: {Id}", id);

    //         var command = new Applications.Features.MerchantInvoiceOrder.Commands.DeactivateMerchantInvoiceOrderCommand(id);
    //         var result = await mediator.Send(command);

    //         if (result.Code == "000" && result.Data == true)
    //         {
    //             logger.LogInformation("Successfully deactivated MerchantInvoiceOrder with Id: {Id}", id);
    //             return Ok(result);
    //         }

    //         if (result.Code == "404")
    //         {
    //             return NotFound(result);
    //         }

    //         return BadRequest(result);
    //     }
    //     catch (Exception ex)
    //     {
    //         logger.LogError(ex, "Error occurred while deactivating MerchantInvoiceOrder with Id: {Id}", id);
    //         return StatusCode(500, new Response<object>
    //         {
    //             Message = "An internal server error occurred while deactivating MerchantInvoiceOrder",
    //             Code = ErrorCodes.INTERNAL_SERVER_ERROR
    //         });
    //     }
    // }
}
